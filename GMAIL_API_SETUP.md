# Gmail API Setup Guide

## Overview
This guide explains how to set up Gmail API for sending emails from your wildlife safari website.

## Prerequisites
- Google account with Gmail enabled
- Google Cloud Console access

## Setup Steps

### 1. Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Gmail API:
   - Go to "APIs & Services" > "Library"
   - Search for "Gmail API"
   - Click "Enable"

### 2. Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Web application"
4. Add authorized redirect URIs:
   - `http://localhost:3000` (for development)
   - Your production domain
5. Download the credentials JSON file

### 3. Get Refresh Token
You need to get a refresh token to authenticate your application. Use this Node.js script:

```javascript
const { google } = require('googleapis');
const readline = require('readline');

// Replace with your credentials
const CLIENT_ID = 'your_client_id';
const CLIENT_SECRET = 'your_client_secret';
const REDIRECT_URI = 'http://localhost:3000';

const oauth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);

// Generate auth URL
const authUrl = oauth2Client.generateAuthUrl({
  access_type: 'offline',
  scope: ['https://www.googleapis.com/auth/gmail.send'],
});

console.log('Authorize this app by visiting this url:', authUrl);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.question('Enter the code from that page here: ', (code) => {
  rl.close();
  oauth2Client.getToken(code, (err, token) => {
    if (err) return console.error('Error retrieving access token', err);
    console.log('Refresh Token:', token.refresh_token);
    console.log('Access Token:', token.access_token);
  });
});
```

### 4. Update Email Service Configuration
In `src/services/emailService.ts`, update the `GMAIL_API_CONFIG` object:

```typescript
const GMAIL_API_CONFIG = {
  CLIENT_ID: 'your_actual_client_id',
  CLIENT_SECRET: 'your_actual_client_secret',
  REFRESH_TOKEN: 'your_actual_refresh_token',
  ACCESS_TOKEN: 'your_actual_access_token', // Optional, will be refreshed automatically
  FROM_EMAIL: '<EMAIL>',
  TO_EMAIL: '<EMAIL>'
};
```

### 5. Environment Variables (Recommended)
For security, store credentials in environment variables:

Create `.env` file:
```
GMAIL_CLIENT_ID=your_client_id
GMAIL_CLIENT_SECRET=your_client_secret
GMAIL_REFRESH_TOKEN=your_refresh_token
GMAIL_FROM_EMAIL=<EMAIL>
GMAIL_TO_EMAIL=<EMAIL>
```

Then update the config:
```typescript
const GMAIL_API_CONFIG = {
  CLIENT_ID: import.meta.env.VITE_GMAIL_CLIENT_ID,
  CLIENT_SECRET: import.meta.env.VITE_GMAIL_CLIENT_SECRET,
  REFRESH_TOKEN: import.meta.env.VITE_GMAIL_REFRESH_TOKEN,
  ACCESS_TOKEN: '',
  FROM_EMAIL: import.meta.env.VITE_GMAIL_FROM_EMAIL,
  TO_EMAIL: import.meta.env.VITE_GMAIL_TO_EMAIL
};
```

## Security Notes
- Never commit credentials to version control
- Use environment variables for production
- Refresh tokens don't expire unless revoked
- Access tokens expire after 1 hour (handled automatically)

## Testing
1. Make a test booking or contact form submission
2. Check browser console for success/error messages
3. Check your Gmail inbox for received emails

## Troubleshooting
- **401 Unauthorized**: Check your credentials
- **403 Forbidden**: Ensure Gmail API is enabled
- **Invalid grant**: Refresh token may be expired, generate new one
- **CORS errors**: Gmail API calls are made from frontend, ensure proper CORS setup

## Email Templates
The system includes three HTML email templates:
- Booking confirmations
- Custom tour requests  
- Contact form submissions

All templates are responsive and include your branding.
