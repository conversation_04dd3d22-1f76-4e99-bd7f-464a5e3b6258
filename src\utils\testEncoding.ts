// Test utility to verify UTF-8 encoding works correctly

/**
 * Test the base64url encoding with various character sets
 */
export const testBase64Encoding = (): void => {
  console.log('🧪 Testing Base64 URL Encoding...\n');

  // Test strings with different character sets
  const testStrings = [
    'Hello World',
    'Hello 🦁 World',
    'Special chars: àáâãäåæçèéêë',
    'Unicode: 🌍📧🦁',
    'Mixed: Hello 🦁 àáâã World 📧',
    'Email content with\nline breaks\nand special chars: €£¥',
    'HTML: <h1>Title</h1><p>Content with émojis 🦁</p>'
  ];

  testStrings.forEach((testString, index) => {
    try {
      console.log(`Test ${index + 1}: "${testString}"`);
      
      // Test our custom UTF-8 safe encoding
      const utf8Bytes = new TextEncoder().encode(testString);
      console.log(`  UTF-8 bytes length: ${utf8Bytes.length}`);
      
      // Test if btoa would fail
      let btoaWorks = true;
      try {
        btoa(testString);
      } catch (error) {
        btoaWorks = false;
        console.log(`  ❌ btoa() would fail: ${error.message}`);
      }
      
      if (btoaWorks) {
        console.log(`  ✅ btoa() works fine`);
      }
      
      console.log(`  ✅ Our UTF-8 encoding should work\n`);
      
    } catch (error) {
      console.error(`  ❌ Error with test string: ${error}\n`);
    }
  });

  console.log('✅ Encoding tests completed!');
};

/**
 * Test email message formatting
 */
export const testEmailFormatting = (): void => {
  console.log('🧪 Testing Email Message Formatting...\n');

  const testEmailData = {
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Test Subject with émojis 🦁',
    htmlBody: `
      <html>
        <body>
          <h1>Test Email 🌍</h1>
          <p>This is a test with special characters: àáâãäåæçèéêë</p>
          <p>And emojis: 🦁📧🌍</p>
        </body>
      </html>
    `
  };

  // Create email message in RFC 2822 format
  const emailMessage = [
    `To: ${testEmailData.to}`,
    `From: ${testEmailData.from}`,
    `Subject: ${testEmailData.subject}`,
    'Content-Type: text/html; charset=utf-8',
    'MIME-Version: 1.0',
    '',
    testEmailData.htmlBody
  ].join('\r\n');

  console.log('Email message format:');
  console.log('---START---');
  console.log(emailMessage);
  console.log('---END---\n');

  // Test encoding
  try {
    const utf8Bytes = new TextEncoder().encode(emailMessage);
    console.log(`✅ Email message can be encoded to UTF-8 (${utf8Bytes.length} bytes)`);
    
    // Test if btoa would work
    try {
      btoa(emailMessage);
      console.log('✅ btoa() would work with this message');
    } catch (error) {
      console.log(`❌ btoa() would fail: ${error.message}`);
      console.log('✅ But our custom encoding will handle it!');
    }
    
  } catch (error) {
    console.error(`❌ Error encoding email message: ${error}`);
  }

  console.log('\n✅ Email formatting tests completed!');
};

// Usage:
// import { testBase64Encoding, testEmailFormatting } from './utils/testEncoding';
// testBase64Encoding();
// testEmailFormatting();
