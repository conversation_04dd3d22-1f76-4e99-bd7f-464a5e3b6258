<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classic Safaris - A Curated Journey</title>
    
    <style>
        /* --- Google Fonts --- */
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Playfair+Display:wght@400;700&display=swap');

        /* --- CSS Variables --- */
        :root {
            --bg-color: #F5F1EB;
            --card-bg-color: #EAE3D6;
            --text-color: #4B4237;
            --heading-color: #3C352D;
            --accent-color: #B95E2D;
            --font-serif: 'Playfair Display', serif;
            --font-sans: 'Montserrat', sans-serif;
        }

        /* --- Basic Reset & Body Styles --- */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-serif);
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            overflow: hidden; /* Prevent body scroll to allow custom swipe */
            height: 100vh;
        }

        /* --- Main Layout Container --- */
        .container {
            display: grid;
            grid-template-columns: 45% 55%;
            height: 100vh;
            width: 100%;
        }

        /* --- Left Intro Panel --- */
        .intro-panel {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5vw;
        }

        .intro-content {
            max-width: 450px;
        }

        .subheading {
            font-family: var(--font-sans);
            font-size: 0.75rem;
            letter-spacing: 0.2em;
            font-weight: 700;
            color: var(--text-color);
            opacity: 0.8;
            text-transform: uppercase;
        }

        h1 {
            font-family: var(--font-serif);
            font-size: clamp(2.5rem, 4vw, 3.2rem); /* Responsive font size */
            line-height: 1.2;
            margin: 1rem 0 1.5rem;
            color: var(--heading-color);
            font-weight: 400;
        }

        .intro-content p {
            font-size: 0.9rem;
            margin-bottom: 2rem;
            max-width: 40ch; /* Improves readability */
        }

        .cta-button {
            display: inline-block;
            background-color: var(--accent-color);
            color: var(--bg-color);
            font-family: var(--font-sans);
            font-weight: 700;
            text-decoration: none;
            font-size: 0.8rem;
            letter-spacing: 0.15em;
            padding: 1rem 2rem;
            transition: background-color 0.3s ease;
            text-transform: uppercase;
        }

        .cta-button:hover {
            background-color: #a05126; /* Darker accent for hover */
        }

        /* --- Right Cards Panel (The Swipable Area) --- */
        .cards-panel {
            height: 100vh;
            overflow: hidden; /* This creates the viewport for our swiper */
            cursor: grab;
            user-select: none; /* Prevents text selection while dragging */
        }

        .cards-panel.grabbing {
            cursor: grabbing;
        }

        .cards-wrapper {
            /* This element will be moved by JavaScript */
            will-change: transform;
            padding: 2.5rem 0; /* Add some space at top and bottom */
        }

        .card {
            width: 80%;
            max-width: 400px;
            margin: 0 auto 2rem auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            /* To ensure a clean look */
            overflow: hidden;
            border-radius: 4px;
        }

        .card img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            display: block;
        }

        .card-content {
            background-color: var(--card-bg-color);
            padding: 1.5rem;
        }

        .card-subheading {
            font-family: var(--font-sans);
            font-size: 0.65rem;
            letter-spacing: 0.15em;
            font-weight: 700;
            color: var(--text-color);
            opacity: 0.8;
            display: block;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
        }

        .card-content h2 {
            font-family: var(--font-serif);
            font-size: 1.5rem;
            line-height: 1.3;
            color: var(--heading-color);
            margin-bottom: 0.5rem;
            font-weight: 400;
        }

        .card-content p {
            font-family: var(--font-sans);
            font-size: 0.85rem;
            color: var(--text-color);
            line-height: 1.5;
        }

        /* --- Basic Responsiveness --- */
        @media (max-width: 900px) {
            body {
                overflow: auto; /* Re-enable native body scroll on mobile */
                height: auto;
            }
            .container {
                grid-template-columns: 1fr; /* Stack columns vertically */
                height: auto;
            }
            .intro-panel {
                text-align: center;
                padding: 4rem 2rem;
            }
            .intro-content {
                max-width: none;
            }
            .intro-content p {
                 margin-left: auto;
                 margin-right: auto;
            }
            .cards-panel {
                height: auto;
                overflow: visible; /* Disable swipe viewport */
                cursor: default;
                user-select: auto;
            }
            .cards-wrapper {
                transform: none !important; /* IMPORTANT: Reset JS transform */
                padding: 0 0 2rem 0;
            }
            .card {
                margin: 0 auto 2rem auto;
            }
        }
    </style>
</head>
<body>

    <main class="container">
        <!-- Left static content panel -->
        <div class="intro-panel">
            <div class="intro-content">
                <span class="subheading">Unforgettable Journeys</span>
                <h1>Our Classic Safaris,<br>Timeless Journeys,<br>Masterfully Curated.</h1>
                <p>Imagine Africa, perfectly planned. Our expert team meticulously crafts classic safari tours, ensuring every detail is seamless. These tried and tested itineraries mean you simply enjoy a luxurious, worry-free adventure.</p>
                <a href="#" class="cta-button">Explore More</a>
            </div>
        </div>

        <!-- Right swipable cards panel -->
        <div class="cards-panel">
            <div class="cards-wrapper">
                <div class="card">
                    <img src="https://images.unsplash.com/photo-1557050543-4d5f4e07d7c2?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800" alt="Two large elephants in the Serengeti with a hot air balloon in the background">
                    <div class="card-content">
                        <span class="card-subheading">7 DAYS</span>
                        <h2>Serengeti Safari Adventure</h2>
                        <p>Delve into the safari that takes you through Tanzania's most famous national parks, offering unparalleled wildlife viewing, including...</p>
                    </div>
                </div>
                
                <div class="card">
                    <img src="https://images.unsplash.com/photo-1549488344-cbb6c34cf08b?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800" alt="A lioness resting on a thick tree branch">
                    <div class="card-content">
                        <span class="card-subheading">5 DAYS</span>
                        <h2>Ngorongoro Crater and Lake Manyara...</h2>
                        <p>A shorter safari focusing on two of Tanzania's most unique ecosystems, ideal for travelers with limited time but a desire for...</p>
                    </div>
                </div>

                <div class="card">
                    <img src="https://images.unsplash.com/photo-1589395937658-0557e7d89fad?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800" alt="Three giraffes standing in a field with Mount Kilimanjaro in the background">
                    <div class="card-content">
                        <span class="card-subheading">7 - 10 DAYS</span>
                        <h2>Mount Kilimanjaro Trek</h2>
                        <p>A challenging yet rewarding adventure to summit Africa's highest peak, offering breathtaking views and a sense of accomplishme...</p>
                    </div>
                </div>

                <div class="card">
                     <img src="https://images.unsplash.com/photo-1531332247349-3e5f0538fb69?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800" alt="Maasai tribe members in traditional red clothing dancing">
                    <div class="card-content">
                        <span class="card-subheading">3 DAYS</span>
                        <h2>Maasai Cultural Experience</h2>
                        <p>Immerse yourself in the vibrant culture of the Maasai people, learning about their ancient traditions, ceremonies, and daily life in the...</p>
                    </div>
                </div>
                
                 <div class="card">
                    <img src="https://images.unsplash.com/photo-1601758177266-bc5a588b6a24?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800" alt="A tranquil beach in Zanzibar with a traditional dhow boat">
                    <div class="card-content">
                        <span class="card-subheading">6 DAYS</span>
                        <h2>Zanzibar's Spice Islands</h2>
                        <p>Relax on pristine white-sand beaches, explore historic Stone Town, and discover the aromatic spice farms of this exotic archipelago...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        const cardsPanel = document.querySelector('.cards-panel');
        const cardsWrapper = document.querySelector('.cards-wrapper');
        
        // Check if the media query for mobile view is active. If so, don't run the script.
        const isMobile = window.matchMedia("(max-width: 900px)").matches;

        if (cardsPanel && cardsWrapper && !isMobile) {
            let state = {
                isDragging: false,
                startY: 0,
                startTranslate: 0,
                currentTranslate: 0,
                lastTranslate: 0,
                velocity: 0,
                animationFrame: 0,
            };

            const getPositionY = e => e.type.includes('mouse') ? e.pageY : e.touches[0].clientY;

            function dragStart(e) {
                // Prevent default behavior (like page scrolling on touch)
                if(e.type === 'touchstart') e.preventDefault();

                state.isDragging = true;
                state.startY = getPositionY(e);
                state.startTranslate = state.currentTranslate;
                state.velocity = 0;
                
                cardsPanel.classList.add('grabbing');
                
                // Cancel any ongoing momentum animation
                cancelAnimationFrame(state.animationFrame);
                // Start tracking movement for velocity calculation
                state.animationFrame = requestAnimationFrame(animationLoop);
            }

            function drag(e) {
                if (!state.isDragging) return;
                const currentY = getPositionY(e);
                const diff = currentY - state.startY;
                state.currentTranslate = state.startTranslate + diff;
            }

            function dragEnd() {
                if (!state.isDragging) return;
                state.isDragging = false;
                cardsPanel.classList.remove('grabbing');
                
                // Stop the drag-tracking loop
                cancelAnimationFrame(state.animationFrame);
                
                // Start the momentum animation
                state.animationFrame = requestAnimationFrame(momentumLoop);
            }
            
            function animationLoop() {
                if (!state.isDragging) return;
                
                // Calculate velocity based on how much it moved since the last frame
                const moved = state.currentTranslate - state.lastTranslate;
                state.velocity = moved * 5; // A multiplier for a snappier feel
                state.lastTranslate = state.currentTranslate;

                setTranslate(false); // Update position with rubber-band effect
                state.animationFrame = requestAnimationFrame(animationLoop);
            }

            function momentumLoop() {
                state.currentTranslate += state.velocity;
                state.velocity *= 0.94; // Apply friction to slow it down

                // If it hits a boundary or slows to a halt, stop.
                if (setTranslate(true) || Math.abs(state.velocity) < 0.1) {
                    cancelAnimationFrame(state.animationFrame);
                    return;
                }
                
                state.animationFrame = requestAnimationFrame(momentumLoop);
            }
            
            function setTranslate(snapToBounds) {
                const panelHeight = cardsPanel.offsetHeight;
                const wrapperHeight = cardsWrapper.scrollHeight;
                const maxTranslate = 0;
                // Only allow scrolling if content is taller than the panel
                const minTranslate = panelHeight - wrapperHeight < 0 ? panelHeight - wrapperHeight : 0;
                
                let boundaryHit = false;

                if (snapToBounds) {
                    // Snap back to boundaries after dragging/momentum
                    if (state.currentTranslate > maxTranslate) {
                        state.currentTranslate = maxTranslate;
                        boundaryHit = true;
                    } else if (state.currentTranslate < minTranslate) {
                        state.currentTranslate = minTranslate;
                        boundaryHit = true;
                    }
                } else {
                    // Apply a "rubber band" effect when dragging past boundaries
                    if (state.currentTranslate > maxTranslate) {
                        state.currentTranslate = maxTranslate + (state.currentTranslate - maxTranslate) * 0.4;
                    } else if (state.currentTranslate < minTranslate) {
                        state.currentTranslate = minTranslate + (state.currentTranslate - minTranslate) * 0.4;
                    }
                }

                cardsWrapper.style.transform = `translateY(${state.currentTranslate}px)`;
                return boundaryHit;
            }
            
            // --- Attach Event Listeners ---

            // Mouse events
            cardsPanel.addEventListener('mousedown', dragStart);
            window.addEventListener('mousemove', drag); // Listen on window to allow dragging outside the panel
            window.addEventListener('mouseup', dragEnd);
            
            // Touch events
            cardsPanel.addEventListener('touchstart', dragStart, { passive: false });
            window.addEventListener('touchmove', drag, { passive: false });
            window.addEventListener('touchend', dragEnd);
            
            // Prevent default browser dragging on images and links
            cardsWrapper.querySelectorAll('img, a').forEach(el => {
                el.addEventListener('dragstart', (e) => e.preventDefault());
            });
        }
    </script>

</body>
</html>