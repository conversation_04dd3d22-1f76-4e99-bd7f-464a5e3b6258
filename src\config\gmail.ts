// Gmail API Configuration
// For security, these should be stored in environment variables

export const GMAIL_CONFIG = {
  CLIENT_ID: import.meta.env.VITE_GMAIL_CLIENT_ID || 'your_gmail_client_id',
  CLIENT_SECRET: import.meta.env.VITE_GMAIL_CLIENT_SECRET || 'your_gmail_client_secret',
  REFRESH_TOKEN: import.meta.env.VITE_GMAIL_REFRESH_TOKEN || 'your_gmail_refresh_token',
  ACCESS_TOKEN: import.meta.env.VITE_GMAIL_ACCESS_TOKEN || '',
  FROM_EMAIL: import.meta.env.VITE_GMAIL_FROM_EMAIL || '<EMAIL>',
  TO_EMAIL: import.meta.env.VITE_GMAIL_TO_EMAIL || '<EMAIL>'
};

// Validation function to check if all required config is present
export const validateGmailConfig = (): boolean => {
  const required = ['CLIENT_ID', 'CLIENT_SECRET', 'REFRESH_TOKEN', 'FROM_EMAIL', 'TO_EMAIL'];
  
  for (const key of required) {
    const value = GMAIL_CONFIG[key as keyof typeof GMAIL_CONFIG];
    if (!value || value.startsWith('your_gmail_')) {
      console.error(`Gmail API configuration missing or invalid: ${key}`);
      return false;
    }
  }
  
  return true;
};
