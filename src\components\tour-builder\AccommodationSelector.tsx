
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Star } from 'lucide-react';

interface AccommodationOption {
  id: string;
  name: string;
  level: string;
  description: string;
  features: string[];
  priceMultiplier: number;
  image: string;
}

interface AccommodationSelectorProps {
  selectedAccommodation: string;
  onAccommodationChange: (accommodation: string) => void;
  destinations: string[];
}

const AccommodationSelector: React.FC<AccommodationSelectorProps> = ({
  selectedAccommodation,
  onAccommodationChange,
  destinations
}) => {
  const accommodationOptions: AccommodationOption[] = [
    {
      id: 'budget',
      name: 'Budget Safari',
      level: 'Budget',
      description: 'Comfortable camping and budget lodges with essential amenities',
      features: ['Camping/Budget Lodges', 'Shared Facilities', 'Basic Meals', 'Standard Safari Vehicle'],
      priceMultiplier: 1.0,
      image: 'photo-1472396961693-142e6e269027'
    },
    {
      id: 'midrange',
      name: 'Mid-Range Safari',
      level: 'Mid-Range',
      description: 'Comfortable lodges and tented camps with good amenities',
      features: ['Tented Camps/Lodges', 'Private Bathrooms', 'Quality Meals', 'Pop-up Roof Vehicle'],
      priceMultiplier: 1.5,
      image: 'photo-1466721591366-2d5fba72006d'
    },
    {
      id: 'luxury',
      name: 'Luxury Safari',
      level: 'Luxury',
      description: 'Premium lodges and camps with exceptional service and amenities',
      features: ['Luxury Lodges', 'Spacious Suites', 'Gourmet Dining', 'Premium Vehicle', 'Spa Services'],
      priceMultiplier: 2.5,
      image: 'photo-1493962853295-0fd70327578a'
    },
    {
      id: 'ultra',
      name: 'Ultra Luxury Safari',
      level: 'Ultra Luxury',
      description: 'The ultimate safari experience with exclusive lodges and personalized service',
      features: ['Exclusive Ultra-Luxury Lodges', 'Private Butler Service', 'Michelin-Star Dining', 'Private Aircraft', 'Personal Guide', 'Unlimited Premium Beverages'],
      priceMultiplier: 4.0,
      image: 'photo-1578662996442-48f60103fc96'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-4">Choose Your Accommodation Level</h2>
        <p className="text-gray-600">
          Select the accommodation style that best fits your preferences and budget
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {accommodationOptions.map((option) => (
          <Card 
            key={option.id}
            className={`cursor-pointer transition-all h-full ${
              selectedAccommodation === option.id 
                ? 'ring-2 ring-orange-500 bg-orange-50' 
                : 'hover:shadow-lg'
            }`}
            onClick={() => onAccommodationChange(option.id)}
          >
            <div className="relative">
              <img
                src={`https://images.unsplash.com/${option.image}?auto=format&fit=crop&w=400&h=200`}
                alt={option.name}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              {selectedAccommodation === option.id && (
                <div className="absolute top-4 right-4 bg-orange-600 text-white rounded-full p-2">
                  <Check className="h-4 w-4" />
                </div>
              )}
              <Badge className="absolute bottom-4 left-4 bg-white text-gray-900">
                {option.level}
              </Badge>
            </div>
            
            <CardContent className="p-6 flex-1 flex flex-col">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-xl font-semibold">{option.name}</h3>
                <div className="flex">
                  {[...Array(option.level === 'Budget' ? 2 : option.level === 'Mid-Range' ? 3 : 5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                  ))}
                </div>
              </div>
              
              <p className="text-gray-600 mb-4 flex-1">{option.description}</p>
              
              <div className="space-y-2">
                <h4 className="font-semibold text-sm">Includes:</h4>
                <ul className="space-y-1">
                  {option.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <Check className="h-3 w-3 text-green-600 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="mt-4 pt-4 border-t">
                <div className="text-center">
                  <span className="text-sm text-gray-500">Price multiplier: </span>
                  <span className="font-semibold">{option.priceMultiplier}x</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedAccommodation && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center">
              <Check className="h-5 w-5 text-green-600 mr-2" />
              <span className="font-semibold">
                {accommodationOptions.find(opt => opt.id === selectedAccommodation)?.name} selected
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AccommodationSelector;
