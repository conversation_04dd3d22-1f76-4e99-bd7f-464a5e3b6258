import React from 'react';

interface PageLoaderProps {
  title?: string;
  subtitle?: string;
  className?: string;
  darkTheme?: boolean;
}

const PageLoader: React.FC<PageLoaderProps> = ({
  title = "Loading...",
  subtitle = "Please wait while we prepare your experience...",
  className = "",
  darkTheme = false
}) => {
  return (
    <div className={`${darkTheme ? 'bg-[#16191D]' : 'bg-background'} font-sans ${darkTheme ? 'text-[#F2EEE6]' : 'text-foreground'} text-[14px] leading-[1.3] antialiased min-h-screen ${className}`}>
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className={`font-cinzel text-[27px] tracking-[-1px] mb-4 ${darkTheme ? 'text-[#D4C2A4]' : ''}`}>{title}</div>
          <div className={`animate-pulse ${darkTheme ? 'text-[#F2EEE6]/70' : ''}`}>{subtitle}</div>
        </div>
      </div>
    </div>
  );
};

export default PageLoader;
