// Test utility for Gmail API integration
// This file can be used to test the Gmail API setup

import { EmailService, BookingEmailData, CustomTourEmailData } from '../services/emailService';

// Test booking email
export const testBookingEmail = async (): Promise<void> => {
  const testBookingData: BookingEmailData = {
    tourTitle: 'Test Safari Adventure',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    startDate: '2025-08-15',
    groupSize: 2,
    childrenCount: 0,
    accommodation: 'luxury',
    totalPrice: 2500,
    specialRequests: 'Vegetarian meals please',
    bookingId: 'TEST_BOOKING_001',
    travelers: [
      {
        name: '<PERSON>',
        age: 35,
        nationality: 'American',
        dietaryRequirements: 'Vegetarian'
      },
      {
        name: '<PERSON>',
        age: 32,
        nationality: 'American',
        dietaryRequirements: 'None'
      }
    ],
    addOns: ['photography', 'cultural']
  };

  console.log('Testing booking email...');
  const result = await EmailService.sendBookingNotification(testBookingData);
  console.log('Booking email test result:', result ? 'SUCCESS' : 'FAILED');
};

// Test custom tour email
export const testCustomTourEmail = async (): Promise<void> => {
  const testTourData: CustomTourEmailData = {
    customerName: 'Alice Smith',
    customerEmail: '<EMAIL>',
    customerPhone: '+1987654321',
    duration: 7,
    participants: 4,
    budget: [3000, 5000],
    startDate: '2025-09-01',
    destinations: ['Serengeti', 'Ngorongoro Crater'],
    interests: ['Wildlife Photography', 'Bird Watching'],
    accommodation: 'luxury',
    activities: ['Game Drives', 'Walking Safari'],
    specialRequests: 'Professional photography guide needed',
    fitnessLevel: 'moderate',
    photographyInterest: true,
    requestId: 'TEST_CUSTOM_001'
  };

  console.log('Testing custom tour email...');
  const result = await EmailService.sendCustomTourNotification(testTourData);
  console.log('Custom tour email test result:', result ? 'SUCCESS' : 'FAILED');
};

// Test contact email
export const testContactEmail = async (): Promise<void> => {
  const testContactData = {
    name: 'Bob Johnson',
    email: '<EMAIL>',
    phone: '+1555123456',
    subject: 'Question about Safari Packages',
    message: 'Hi, I would like to know more about your 5-day safari packages. What is included in the price?',
    category: 'General Inquiry'
  };

  console.log('Testing contact email...');
  const result = await EmailService.sendContactNotification(testContactData);
  console.log('Contact email test result:', result ? 'SUCCESS' : 'FAILED');
};

// Run all tests
export const runAllEmailTests = async (): Promise<void> => {
  console.log('🧪 Starting Gmail API Email Tests...\n');
  
  await testBookingEmail();
  console.log('');
  
  await testCustomTourEmail();
  console.log('');
  
  await testContactEmail();
  console.log('');
  
  console.log('✅ All email tests completed!');
};

// Usage example:
// import { runAllEmailTests } from './utils/testGmailAPI';
// runAllEmailTests();
